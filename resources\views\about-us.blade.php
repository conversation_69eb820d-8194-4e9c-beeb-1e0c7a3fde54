<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="scroll-smooth">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>About Us - VSMART TUNE UP</title>
        <meta name="description" content="Learn about VSMART TUNE UP - our mission, milestones, and the team behind our professional repair services.">
        
        <!-- Favicon -->
        <link rel="icon" type="image/png" href="{{ asset('img/LogoClear.png') }}">
        
        <!-- Open Graph / Facebook -->
        <meta property="og:type" content="website">
        <meta property="og:url" content="{{ url('/about-us') }}">
        <meta property="og:title" content="About Us - VSMART TUNE UP">
        <meta property="og:description" content="Learn about VSMART TUNE UP - our mission, milestones, and the team behind our professional repair services.">
        <meta property="og:image" content="{{ asset('img/og-image.jpg', true) ?? 'https://placehold.co/1200x630/red/white?text=VSMART+SYSTEM' }}">
        
        <!-- Twitter -->
        <meta property="twitter:card" content="summary_large_image">
        <meta property="twitter:url" content="{{ url('/about-us') }}">
        <meta property="twitter:title" content="About Us - VSMART TUNE UP">
        <meta property="twitter:description" content="Learn about VSMART TUNE UP - our mission, milestones, and the team behind our professional repair services.">
        <meta property="twitter:image" content="{{ asset('img/og-image.jpg', true) ?? 'https://placehold.co/1200x630/red/white?text=VSMART+SYSTEM' }}">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600,700" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&display=swap" rel="stylesheet">

        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

        <!-- Styles / Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])

        <!-- Alpine.js -->
        <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
        
        <!-- AOS Animation Library -->
        <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
        <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
        
        <style>
            [x-cloak] { display: none !important; }
            
            body {
                font-family: 'Poppins', sans-serif;
                background-color: #000;
                color: #fff;
                position: relative;
                overflow-x: hidden;
            }
            
            /* Animated Background Elements */
            .bg-particles {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: -1;
                pointer-events: none;
            }
            
            .particle {
                position: absolute;
                background: radial-gradient(circle, rgba(239,68,68,0.1) 0%, transparent 70%);
                border-radius: 50%;
                animation: float 20s infinite linear;
            }
            
            .floating-icon {
                position: fixed;
                font-size: 3rem;
                opacity: 0.2;
                pointer-events: none;
                z-index: -1;
                animation: floatIcon 15s infinite linear;
                text-shadow: 0 0 10px rgba(239,68,68,0.3);
            }
            
            .floating-icon:nth-child(1) { color: #ef4444; top: 10%; left: 5%; animation-delay: 0s; }
            .floating-icon:nth-child(2) { color: #3b82f6; top: 20%; right: 10%; animation-delay: -2s; }
            .floating-icon:nth-child(3) { color: #10b981; bottom: 30%; left: 15%; animation-delay: -4s; }
            .floating-icon:nth-child(4) { color: #f59e0b; top: 40%; right: 20%; animation-delay: -6s; }
            .floating-icon:nth-child(5) { color: #8b5cf6; bottom: 20%; right: 15%; animation-delay: -8s; }
            .floating-icon:nth-child(6) { color: #ec4899; top: 60%; left: 25%; animation-delay: -10s; }
            .floating-icon:nth-child(7) { color: #06b6d4; bottom: 40%; left: 35%; animation-delay: -12s; }
            .floating-icon:nth-child(8) { color: #f97316; top: 30%; right: 30%; animation-delay: -14s; }
            
            @keyframes floatIcon {
                0% {
                    transform: translate(0, 0) rotate(0deg) scale(1);
                    opacity: 0.2;
                }
                25% {
                    transform: translate(100px, 100px) rotate(90deg) scale(1.2);
                    opacity: 0.3;
                }
                50% {
                    transform: translate(0, 200px) rotate(180deg) scale(1);
                    opacity: 0.2;
                }
                75% {
                    transform: translate(-100px, 100px) rotate(270deg) scale(0.8);
                    opacity: 0.3;
                }
                100% {
                    transform: translate(0, 0) rotate(360deg) scale(1);
                    opacity: 0.2;
                }
            }
            
            .grid-pattern {
                background-image: 
                    linear-gradient(to right, rgba(34,34,34,0.1) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(34,34,34,0.1) 1px, transparent 1px);
                background-size: 30px 30px;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: -1;
                pointer-events: none;
            }
            
            .gradient-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: radial-gradient(circle at center, rgba(239,68,68,0.05) 0%, transparent 70%);
                z-index: -1;
                pointer-events: none;
            }
            
            .section-gradient {
                position: relative;
                overflow: hidden;
            }
            
            .section-gradient::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: radial-gradient(circle at top right, rgba(239,68,68,0.05) 0%, transparent 70%);
                z-index: -1;
            }
            
            .section-gradient::after {
                content: '';
                position: absolute;
                bottom: 0;
                right: 0;
                width: 100%;
                height: 100%;
                background: radial-gradient(circle at bottom left, rgba(239,68,68,0.05) 0%, transparent 70%);
                z-index: -1;
            }
            
            .tech-font {
                font-family: 'Orbitron', sans-serif;
            }
            
            .fade-in {
                opacity: 0;
                transform: translateY(20px);
                transition: opacity 0.5s ease, transform 0.5s ease;
            }
            
            .fade-in.active {
                opacity: 1;
                transform: translateY(0);
            }
            
            .hover-up {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }
            
            .hover-up:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 25px -5px rgba(236, 72, 72, 0.4);
            }
            
            .accent-gradient {
                background: linear-gradient(135deg, #f87171, #ef4444, #dc2626);
            }
            
            .hero-gradient {
                background: linear-gradient(to bottom, rgba(0,0,0,0.85), rgba(15,15,15,0.95));
            }
            
            .timeline-dot {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }
            
            .timeline-item:hover .timeline-dot {
                transform: scale(1.3);
                box-shadow: 0 0 15px rgba(236, 72, 72, 0.6);
            }
            
            .dark-card {
                background: linear-gradient(145deg, #111111, #0a0a0a);
                border: 1px solid #222;
            }
            
            .glow {
                box-shadow: 0 0 15px rgba(236, 72, 72, 0.2);
            }
            
            .glow:hover {
                box-shadow: 0 0 20px rgba(236, 72, 72, 0.4);
            }
            
            .dark-section {
                background-color: #050505;
                border-top: 1px solid #1a1a1a;
                border-bottom: 1px solid #1a1a1a;
            }
            
            .stats-card {
                background: linear-gradient(145deg, #111111, #0a0a0a);
                border: 1px solid #222;
                transition: all 0.3s ease;
            }

            .stats-card:hover {
                transform: translateY(-5px);
                border-color: #ef4444;
                box-shadow: 0 10px 25px -5px rgba(236, 72, 72, 0.4);
            }

            .value-counter {
                font-family: 'Orbitron', sans-serif;
                font-size: 2.5rem;
                background: linear-gradient(135deg, #f87171, #ef4444);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            
            .team-member-card {
                background: linear-gradient(145deg, #111111, #0a0a0a);
                border: 1px solid #222;
                transition: all 0.4s ease;
            }
            
            .team-member-card:hover {
                transform: translateY(-10px);
                border-color: #ef4444;
                box-shadow: 0 10px 25px -5px rgba(236, 72, 72, 0.4);
            }
            
            .team-member-image {
                position: relative;
                overflow: hidden;
                border-radius: 0.5rem 0.5rem 0 0;
            }
            
            .team-member-image img {
                transition: transform 0.5s ease;
            }
            
            .team-member-card:hover .team-member-image img {
                transform: scale(1.05);
            }
            
            .social-links {
                opacity: 0;
                transform: translateY(10px);
                transition: all 0.3s ease;
            }

            .team-member-card:hover .social-links {
                opacity: 1;
                transform: translateY(0);
            }
            
            .social-icon {
                transition: all 0.3s ease;
            }
            
            .social-icon:hover {
                transform: translateY(-3px) scale(1.2);
                color: #ef4444;
            }

            .mission-card {
                background: linear-gradient(145deg, #111111, #0a0a0a);
                border: 1px solid #222;
                transition: all 0.3s ease;
            }

            .mission-card:hover {
                transform: translateY(-5px);
                border-color: #ef4444;
                box-shadow: 0 10px 25px -5px rgba(236, 72, 72, 0.4);
            }

            .scroll-indicator {
                animation: bounce 2s infinite;
            }

            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% {
                    transform: translateY(0);
                }
                40% {
                    transform: translateY(-10px);
                }
                60% {
                    transform: translateY(-5px);
                }
            }
        </style>
    </head>
    <body class="antialiased bg-black" x-data="{}" x-init="AOS.init({duration: 800, once: false, mirror: true, easing: 'ease-out-cubic'})">
        <!-- Background Elements -->
        <div class="bg-particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>
        
        <!-- Floating Tech Icons -->
        <div class="floating-icons">
            <i class="floating-icon fas fa-microchip"></i>
            <i class="floating-icon fas fa-laptop"></i>
            <i class="floating-icon fas fa-mobile-alt"></i>
            <i class="floating-icon fas fa-desktop"></i>
            <i class="floating-icon fas fa-tools"></i>
            <i class="floating-icon fas fa-wrench"></i>
            <i class="floating-icon fas fa-screwdriver"></i>
            <i class="floating-icon fas fa-plug"></i>
        </div>
        
        <div class="grid-pattern"></div>
        <div class="gradient-overlay"></div>

        <!-- Navigation -->
        @include('components.navigation')

        <!-- Main Content -->
        <main>
            <!-- Hero Section -->
            <section class="relative min-h-screen flex items-center justify-center bg-black text-white overflow-hidden section-gradient">
                <!-- Hero Background -->
                <div class="absolute inset-0 z-0">
                    <div class="absolute inset-0 bg-gradient-to-br from-red-900/30 via-black/90 to-black"></div>
                    <div class="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(239,68,68,0.15)_0%,transparent_70%)]"></div>
                    <div class="absolute inset-0 bg-[linear-gradient(45deg,rgba(239,68,68,0.1)_25%,transparent_25%,transparent_50%,rgba(239,68,68,0.1)_50%,rgba(239,68,68,0.1)_75%,transparent_75%,transparent)] bg-[length:100px_100px] opacity-10"></div>
                </div>
                
                <!-- Hero Content -->
                <div class="container mx-auto px-6 py-20 relative z-10">
                    <div class="max-w-4xl mx-auto text-center">
                        <div data-aos="fade-up" data-aos-delay="100">
                            <h1 class="text-6xl md:text-8xl font-bold mb-6">
                                About <span class="tech-font text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-red-700">VSMART</span>
                            </h1>
                            <div class="w-32 h-1 accent-gradient mx-auto rounded-full mb-8"></div>
                            <p class="text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed font-light">
                                Empowering technology repair excellence through innovation, expertise, and customer-centric service.
                            </p>
                            <div class="flex justify-center space-x-4">
                                <a href="#journey" class="px-8 py-3 bg-red-600 hover:bg-red-700 rounded-full transition-all duration-300 transform hover:scale-105">
                                    Our Journey
                                </a>
                                <a href="#team" class="px-8 py-3 border border-red-600 text-red-600 hover:bg-red-600 hover:text-white rounded-full transition-all duration-300 transform hover:scale-105">
                                    Meet Our Team
                                </a>
                            </div>
                            <div class="scroll-indicator mt-12">
                                <svg class="w-6 h-6 mx-auto text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Stats Section -->
            <section class="py-20 dark-section section-gradient" id="stats">
                <div class="container mx-auto px-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                        <div class="stats-card p-8 rounded-lg text-center group" data-aos="fade-up" data-aos-delay="100">
                            <div class="relative">
                                <div class="absolute inset-0 bg-red-500/10 rounded-full blur-xl group-hover:bg-red-500/20 transition-all duration-300"></div>
                                <div class="value-counter mb-4 relative" x-data="{ count: 0 }" x-init="setInterval(() => count < 100 ? count++ : null, 1)">
                                    <span x-text="count">0</span>+
                                </div>
                            </div>
                            <p class="text-gray-400 text-lg font-medium">Happy Customers</p>
                        </div>
                        <div class="stats-card p-8 rounded-lg text-center group" data-aos="fade-up" data-aos-delay="200">
                            <div class="relative">
                                <div class="absolute inset-0 bg-red-500/10 rounded-full blur-xl group-hover:bg-red-500/20 transition-all duration-300"></div>
                                <div class="value-counter mb-4 relative" x-data="{ count: 0 }" x-init="setInterval(() => count < 150 ? count++ : null, 1)">
                                    <span x-text="count">0</span>+
                                </div>
                            </div>
                            <p class="text-gray-400 text-lg font-medium">Devices Serviced</p>
                                    </div>
                        <div class="stats-card p-8 rounded-lg text-center group" data-aos="fade-up" data-aos-delay="300">
                            <div class="relative">
                                <div class="absolute inset-0 bg-red-500/10 rounded-full blur-xl group-hover:bg-red-500/20 transition-all duration-300"></div>
                                <div class="value-counter mb-4 relative">
                                    <span class="text-4xl">Coming</span>+
                                </div>
                            </div>
                            <p class="text-gray-400 text-lg font-medium">Years Experience</p>
                        </div>
                        <div class="stats-card p-8 rounded-lg text-center group" data-aos="fade-up" data-aos-delay="400">
                            <div class="relative">
                                <div class="absolute inset-0 bg-red-500/10 rounded-full blur-xl group-hover:bg-red-500/20 transition-all duration-300"></div>
                                <div class="value-counter mb-4 relative" x-data="{ count: 0 }" x-init="setInterval(() => count < 98 ? count++ : null, 1)">
                                    <span x-text="count">0</span>%
                                </div>
                            </div>
                            <p class="text-gray-400 text-lg font-medium">Customer Satisfaction</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Mission & Vision Section -->
            <section class="py-20 section-gradient">
                <div class="container mx-auto px-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
                        <div class="mission-card p-8 rounded-lg" data-aos="fade-right">
                            <div class="flex items-center gap-4 mb-4">
                                <div class="w-12 h-12 accent-gradient rounded-full flex items-center justify-center">
                                    <i class="fas fa-bullseye text-2xl text-white"></i>
                                </div>
                                <h3 class="text-2xl font-bold text-red-500">Our Mission</h3>
                            </div>
                            <p class="text-gray-300 leading-relaxed">
                                To provide exceptional services with cutting-edge technology and expert craftsmanship, ensuring customer satisfaction and device longevity.
                            </p>
                    </div>
                        <div class="mission-card p-8 rounded-lg" data-aos="fade-left">
                            <div class="flex items-center gap-4 mb-4">
                                <div class="w-12 h-12 accent-gradient rounded-full flex items-center justify-center">
                                    <i class="fas fa-eye text-2xl text-white"></i>
                                </div>
                                <h3 class="text-2xl font-bold text-red-500">Our Vision</h3>
                            </div>
                            <p class="text-gray-300 leading-relaxed">
                                To be the leading force in technology services, setting industry standards for quality, innovation, and customer experience.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Timeline Section -->
            <section class="py-20 section-gradient" id="journey">
                <div class="container mx-auto px-6">
                    <div class="text-center mb-16" data-aos="fade-up">
                        <h2 class="text-4xl font-bold mb-4">Our Journey</h2>
                        <div class="w-24 h-1 accent-gradient mx-auto rounded-full"></div>
                        <p class="text-gray-400 mt-6 max-w-2xl mx-auto">From a solo venture to a professional repair service, our journey is marked by continuous growth and commitment to excellence.</p>
                    </div>
                    
                    <div class="max-w-4xl mx-auto">
                        <!-- Timeline -->
                        <div class="relative">
                            <!-- Vertical Line -->
                            <div class="absolute left-1/2 transform -translate-x-1/2 h-full w-0.5 bg-gradient-to-b from-red-500 via-red-600 to-red-500"></div>
                        
                        <!-- Timeline Items -->
                            <div class="space-y-20">
                                <!-- Foundation -->
                                <div class="relative" data-aos="fade-up">
                                    <div class="flex items-center justify-center mb-4">
                                        <div class="w-6 h-6 rounded-full bg-red-500 glow relative">
                                            <div class="absolute inset-0 rounded-full bg-red-500 animate-ping opacity-75"></div>
                                        </div>
                                    </div>
                                    <div class="mission-card p-8 rounded-lg max-w-xl mx-auto text-center group hover:border-red-500 transition-all duration-300">
                                        <div class="absolute inset-0 bg-gradient-to-r from-red-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                                        <div class="relative z-10">
                                            <div class="flex items-center justify-center gap-3 mb-4">
                                                <i class="fas fa-building text-red-500 text-xl"></i>
                                                <span class="inline-block px-4 py-1 bg-red-500/10 text-red-500 rounded-full text-sm font-medium">August 2024</span>
                                            </div>
                                            <h4 class="text-2xl font-bold mb-4 text-white">Foundation of VSMART TUNE UP</h4>
                                            <p class="text-gray-400 leading-relaxed">Founded by Vincent Jhanrey Jalalon, VSMART TUNE UP began as a solo venture driven by a passion for technology services and customer service. Starting with mobile device repairs, the foundation was set for what would become a comprehensive tech repair service.</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Team Expansion -->
                                <div class="relative" data-aos="fade-up" data-aos-delay="100">
                                    <div class="flex items-center justify-center mb-4">
                                        <div class="w-6 h-6 rounded-full bg-red-500 glow relative">
                                            <div class="absolute inset-0 rounded-full bg-red-500 animate-ping opacity-75"></div>
                                        </div>
                                    </div>
                                    <div class="mission-card p-8 rounded-lg max-w-xl mx-auto text-center group hover:border-red-500 transition-all duration-300">
                                        <div class="absolute inset-0 bg-gradient-to-r from-red-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                                        <div class="relative z-10">
                                            <div class="flex items-center justify-center gap-3 mb-4">
                                                <i class="fas fa-users text-red-500 text-xl"></i>
                                                <span class="inline-block px-4 py-1 bg-red-500/10 text-red-500 rounded-full text-sm font-medium">September 2024</span>
                                            </div>
                                            <h4 class="text-2xl font-bold mb-4 text-white">Team Expansion</h4>
                                            <p class="text-gray-400 leading-relaxed">Sean Dominic Go joined as Technical Partner, bringing advanced technical expertise and diagnostic skills. This partnership marked the beginning of our expanded service capabilities and enhanced service quality.</p>
                                        </div>
                                </div>
                            </div>
                            
                                <!-- Service Expansion -->
                                <div class="relative" data-aos="fade-up" data-aos-delay="150">
                                    <div class="flex items-center justify-center mb-4">
                                        <div class="w-6 h-6 rounded-full bg-red-500 glow relative">
                                            <div class="absolute inset-0 rounded-full bg-red-500 animate-ping opacity-75"></div>
                                        </div>
                                    </div>
                                    <div class="mission-card p-8 rounded-lg max-w-xl mx-auto text-center group hover:border-red-500 transition-all duration-300">
                                        <div class="absolute inset-0 bg-gradient-to-r from-red-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                                        <div class="relative z-10">
                                            <div class="flex items-center justify-center gap-3 mb-4">
                                                <i class="fas fa-laptop text-red-500 text-xl"></i>
                                                <span class="inline-block px-4 py-1 bg-red-500/10 text-red-500 rounded-full text-sm font-medium">October 2024</span>
                                            </div>
                                            <h4 class="text-2xl font-bold mb-4 text-white">Service Expansion</h4>
                                            <p class="text-gray-400 leading-relaxed">Expanded our services to include laptop services, desktop maintenance, and specialized gadget services. This growth allowed us to serve a broader customer base with comprehensive tech solutions.</p>
                                    </div>
                                </div>
                            </div>
                            
                                <!-- Customer Milestone -->
                                <div class="relative" data-aos="fade-up" data-aos-delay="200">
                                    <div class="flex items-center justify-center mb-4">
                                        <div class="w-6 h-6 rounded-full bg-red-500 glow relative">
                                            <div class="absolute inset-0 rounded-full bg-red-500 animate-ping opacity-75"></div>
                                        </div>
                                    </div>
                                    <div class="mission-card p-8 rounded-lg max-w-xl mx-auto text-center group hover:border-red-500 transition-all duration-300">
                                        <div class="absolute inset-0 bg-gradient-to-r from-red-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                                        <div class="relative z-10">
                                            <div class="flex items-center justify-center gap-3 mb-4">
                                                <i class="fas fa-trophy text-red-500 text-xl"></i>
                                                <span class="inline-block px-4 py-1 bg-red-500/10 text-red-500 rounded-full text-sm font-medium">January 2025</span>
                                            </div>
                                            <h4 class="text-2xl font-bold mb-4 text-white">Customer Milestone</h4>
                                            <p class="text-gray-400 leading-relaxed">Reached our first 100 satisfied customers, establishing ourselves as a trusted name in tech services. This achievement reflected our commitment to quality and customer satisfaction.</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Website Launch -->
                                <div class="relative" data-aos="fade-up" data-aos-delay="250">
                                    <div class="flex items-center justify-center mb-4">
                                        <div class="w-6 h-6 rounded-full bg-red-500 glow relative">
                                            <div class="absolute inset-0 rounded-full bg-red-500 animate-ping opacity-75"></div>
                            </div>
                                    </div>
                                    <div class="mission-card p-8 rounded-lg max-w-xl mx-auto text-center group hover:border-red-500 transition-all duration-300">
                                        <div class="absolute inset-0 bg-gradient-to-r from-red-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
                                        <div class="relative z-10">
                                            <div class="flex items-center justify-center gap-3 mb-4">
                                                <i class="fas fa-globe text-red-500 text-xl"></i>
                                                <span class="inline-block px-4 py-1 bg-red-500/10 text-red-500 rounded-full text-sm font-medium">March 2025</span>
                                            </div>
                                            <h4 class="text-2xl font-bold mb-4 text-white">VSMART TUNE UP Site Launch</h4>
                                            <p class="text-gray-400 leading-relaxed">Successfully launched our official website, providing customers with an easy-to-use platform for service inquiries, appointments, and information about our comprehensive services.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Team Section -->
            <section id="team" class="py-20 bg-black relative overflow-hidden">
                <!-- Animated Background Pattern -->
                <div class="absolute inset-0 opacity-30">
                    <div class="absolute inset-0 bg-[linear-gradient(45deg,rgba(239,68,68,0.05)_25%,transparent_25%,transparent_50%,rgba(239,68,68,0.05)_50%,rgba(239,68,68,0.05)_75%,transparent_75%,transparent)] bg-[length:64px_64px]"></div>
                </div>

                <div class="container mx-auto px-6 relative z-10">
                    <!-- Section Header -->
                    <div class="text-center mb-16" data-aos="fade-up">
                        <h2 class="text-4xl font-bold mb-4">Meet Our Team</h2>
                        <div class="w-24 h-1 accent-gradient mx-auto rounded-full"></div>
                        <p class="text-gray-400 mt-6 max-w-2xl mx-auto">The innovative minds shaping the future of tech services</p>
                    </div>
                    
                    <!-- Leaders Cards Container -->
                    <div class="grid lg:grid-cols-2 gap-16 max-w-6xl mx-auto">
                        <!-- Vincent's Card -->
                        <div class="relative group h-full" data-aos="fade-right">
                            <!-- Card Container -->
                            <div class="relative bg-gradient-to-br from-gray-900 to-black rounded-[2rem] p-1 transition-all duration-300 hover:scale-[1.02] h-full">
                                <div class="absolute inset-0 bg-gradient-to-r from-red-600 to-red-500 opacity-0 group-hover:opacity-20 rounded-[2rem] transition-opacity duration-500"></div>
                                
                                <!-- Card Content -->
                                <div class="relative bg-gray-900 rounded-[2rem] p-8 h-full flex flex-col justify-between">
                                    <!-- Top Section -->
                                    <div class="flex flex-col md:flex-row items-center md:items-start text-center md:text-left gap-6">
                                        <!-- Image Container -->
                                        <div class="relative shrink-0">
                                            <div class="w-40 h-40 rounded-2xl overflow-hidden ring-2 ring-red-500/50 group-hover:ring-red-500 transition-all duration-300">
                                                <img src="{{ asset('img/vinz.jpg') }}" alt="Vincent Jhanrey Jalalon" class="w-full h-full object-cover">
                                            </div>
                                            <!-- Animated Corner Accents -->
                                            <div class="absolute -top-1 -left-1 w-4 h-4 border-t-2 border-l-2 border-red-500 group-hover:w-6 group-hover:h-6 transition-all duration-300"></div>
                                            <div class="absolute -bottom-1 -right-1 w-4 h-4 border-b-2 border-r-2 border-red-500 group-hover:w-6 group-hover:h-6 transition-all duration-300"></div>
                                    </div>
                                    
                                        <!-- Text Content -->
                                        <div class="flex-1">
                                            <div class="mb-4">
                                                <h3 class="text-3xl font-bold text-white mb-2 group-hover:text-red-500 transition-colors">Vincent Jalalon</h3>
                                                <p class="text-red-500 font-medium text-lg">Founder & Lead Technician</p>
                                            </div>
                                            <p class="text-gray-300 leading-relaxed">
                                                Visionary founder of VSMART TUNE UP, pioneering affordable and high-quality device services since August 2024.
                                            </p>
                                        </div>
                                        </div>
                                        
                                    <!-- Skills Section -->
                                    <div class="space-y-6">
                                        <!-- Expertise -->
                                        <div>
                                            <h4 class="text-white font-semibold mb-4 flex items-center gap-2">
                                                <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                                                </svg>
                                                Core Expertise
                                            </h4>
                                            <div class="grid grid-cols-2 gap-3">
                                                <div class="flex items-center gap-2 text-gray-300">
                                                    <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                                                    </svg>
                                                    Hardware Diagnostics
                                                </div>
                                                <div class="flex items-center gap-2 text-gray-300">
                                                    <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                                                    </svg>
                                                    Web Developer
                                                </div>
                                                <div class="flex items-center gap-2 text-gray-300">
                                                    <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                                                    </svg>
                                                    Technical Support
                                                </div>
                                                <div class="flex items-center gap-2 text-gray-300">
                                                    <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                                            </svg>
                                                    Customer Service
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Connect Section -->
                                        <div>
                                            <h4 class="text-white font-semibold mb-4 flex items-center gap-2">
                                                <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                                                </svg>
                                                Connect
                                            </h4>
                                            <div class="flex items-center gap-4">
                                                <a href="https://www.facebook.com/vivinz11" target="_blank" class="text-gray-400 hover:text-red-500 transition-colors">
                                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                                    </svg>
                                                </a>
                                                <a href="#" class="text-gray-400 hover:text-red-500 transition-colors">
                                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M12 0C8.74 0 8.333.015 7.053.072 5.775.132 4.905.333 4.14.63c-.789.306-1.459.717-2.126 1.384S.935 3.35.63 4.14C.333 4.905.131 5.775.072 7.053.012 8.333 0 8.74 0 12s.015 3.667.072 4.947c.06 1.277.261 2.148.558 2.913.306.788.717 1.459 1.384 2.126.667.666 1.336 1.079 2.126 1.384.766.296 1.636.499 2.913.558C8.333 23.988 8.74 24 12 24s3.667-.015 4.947-.072c1.277-.06 2.148-.262 2.913-.558.788-.306 1.459-.718 2.126-1.384.666-.667 1.079-1.335 1.384-2.126.296-.765.499-1.636.558-2.913.06-1.28.072-1.687.072-4.947s-.015-3.667-.072-4.947c-.06-1.277-.262-2.149-.558-2.913-.306-.789-.718-1.459-1.384-2.126C21.319 1.347 20.651.935 19.86.63c-.765-.297-1.636-.499-2.913-.558C15.667.012 15.26 0 12 0zm0 2.16c3.203 0 3.585.016 4.85.071 1.17.055 1.805.249 2.227.415.562.217.96.477 1.382.896.419.42.679.819.896 1.381.164.422.36 1.057.413 2.227.057 1.266.07 1.646.07 4.85s-.015 3.585-.074 4.85c-.061 1.17-.256 1.805-.421 2.227-.224.562-.479.96-.899 1.382-.419.419-.824.679-1.38.896-.42.164-1.065.36-2.235.413-1.274.057-1.649.07-4.859.07-3.211 0-3.586-.015-4.859-.074-1.171-.061-1.816-.256-2.236-.421-.569-.224-.96-.479-1.379-.899-.421-.419-.69-.824-.9-1.38-.165-.42-.359-1.065-.42-2.235-.045-1.26-.061-1.649-.061-4.844 0-3.196.016-3.586.061-4.861.061-1.17.255-1.814.42-2.234.21-.57.479-.96.9-1.381.419-.419.81-.689 1.379-.898.42-.166 1.051-.361 2.221-.421 1.275-.045 1.65-.06 4.859-.06l.045.03zm0 3.678c-3.405 0-6.162 2.76-6.162 6.162 0 3.405 2.76 6.162 6.162 6.162 3.405 0 6.162-2.76 6.162-6.162 0-3.405-2.76-6.162-6.162-6.162zM12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm7.846-10.405c0-.795-.646-1.44-1.44-1.44-.795 0-1.44.646-1.44 1.44 0 .794.646 1.439 1.44 1.439.793-.001 1.44-.645 1.44-1.439z"/>
                                                    </svg>
                                                </a>
                                            </div>
                                        </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                        <!-- Sean's Card -->
                        <div class="relative group h-full" data-aos="fade-left">
                            <!-- Card Container -->
                            <div class="relative bg-gradient-to-br from-gray-900 to-black rounded-[2rem] p-1 transition-all duration-300 hover:scale-[1.02] h-full">
                                <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-500 opacity-0 group-hover:opacity-20 rounded-[2rem] transition-opacity duration-500"></div>
                                
                                <!-- Card Content -->
                                <div class="relative bg-gray-900 rounded-[2rem] p-8 h-full flex flex-col justify-between">
                                    <!-- Top Section -->
                                    <div class="flex flex-col md:flex-row items-center md:items-start text-center md:text-left gap-6">
                                        <!-- Image Container -->
                                        <div class="relative shrink-0">
                                            <div class="w-40 h-40 rounded-2xl overflow-hidden ring-2 ring-blue-500/50 group-hover:ring-blue-500 transition-all duration-300">
                                                <img src="{{ asset('img/seanF.jpg') }}" alt="Sean Dominic Go" class="w-full h-full object-cover">
                                            </div>
                                            <!-- Animated Corner Accents -->
                                            <div class="absolute -top-1 -left-1 w-4 h-4 border-t-2 border-l-2 border-blue-500 group-hover:w-6 group-hover:h-6 transition-all duration-300"></div>
                                            <div class="absolute -bottom-1 -right-1 w-4 h-4 border-b-2 border-r-2 border-blue-500 group-hover:w-6 group-hover:h-6 transition-all duration-300"></div>
                                    </div>
                                    
                                        <!-- Text Content -->
                                        <div class="flex-1">
                                            <div class="mb-4">
                                                <h3 class="text-3xl font-bold text-white mb-2 group-hover:text-blue-500 transition-colors">Sean Dominic Go</h3>
                                                <p class="text-blue-500 font-medium text-lg">Technical Partner</p>
                                            </div>
                                            <p class="text-gray-300 leading-relaxed">
                                                Joined in September 2024, bringing advanced expertise in computer systems and hardware diagnostics. Specializes in comprehensive computer repair solutions.
                                            </p>
                                        </div>
                                        </div>
                                        
                                    <!-- Skills Section -->
                                    <div class="space-y-6">
                                        <!-- Expertise -->
                                        <div>
                                            <h4 class="text-white font-semibold mb-4 flex items-center gap-2">
                                                <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/>
                                                </svg>
                                                Core Expertise
                                            </h4>
                                            <div class="grid grid-cols-2 gap-3">
                                                <div class="flex items-center gap-2 text-gray-300">
                                                    <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                                                    </svg>
                                                    Computer Services
                                                </div>
                                                <div class="flex items-center gap-2 text-gray-300">
                                                    <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                                                    </svg>
                                                    Hardware Diagnostics
                                                </div>
                                                <div class="flex items-center gap-2 text-gray-300">
                                                    <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                                                    </svg>
                                                    System Maintenance
                                                </div>
                                                <div class="flex items-center gap-2 text-gray-300">
                                                    <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"/>
                                            </svg>
                                                    Technical Support
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Connect Section -->
                                        <div>
                                            <h4 class="text-white font-semibold mb-4 flex items-center gap-2">
                                                <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"/>
                                                </svg>
                                                Connect
                                            </h4>
                                            <div class="flex items-center gap-4">
                                                <a href="https://www.facebook.com/sean.go.7902" target="_blank" class="text-gray-400 hover:text-blue-500 transition-colors">
                                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                                    </svg>
                                                </a>
                                                <a href="#" class="text-gray-400 hover:text-blue-500 transition-colors">
                                                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                                        <path d="M12 0C8.74 0 8.333.015 7.053.072 5.775.132 4.905.333 4.14.63c-.789.306-1.459.717-2.126 1.384S.935 3.35.63 4.14C.333 4.905.131 5.775.072 7.053.012 8.333 0 8.74 0 12s.015 3.667.072 4.947c.06 1.277.261 2.148.558 2.913.306.788.717 1.459 1.384 2.126.667.666 1.336 1.079 2.126 1.384.766.296 1.636.499 2.913.558C8.333 23.988 8.74 24 12 24s3.667-.015 4.947-.072c1.277-.06 2.148-.262 2.913-.558.788-.306 1.459-.718 2.126-1.384.666-.667 1.079-1.335 1.384-2.126.296-.765.499-1.636.558-2.913.06-1.28.072-1.687.072-4.947s-.015-3.667-.072-4.947c-.06-1.277-.262-2.149-.558-2.913-.306-.789-.718-1.459-1.384-2.126C21.319 1.347 20.651.935 19.86.63c-.765-.297-1.636-.499-2.913-.558C15.667.012 15.26 0 12 0zm0 2.16c3.203 0 3.585.016 4.85.071 1.17.055 1.805.249 2.227.415.562.217.96.477 1.382.896.419.42.679.819.896 1.381.164.422.36 1.057.413 2.227.057 1.266.07 1.646.07 4.85s-.015 3.585-.074 4.85c-.061 1.17-.256 1.805-.421 2.227-.224.562-.479.96-.899 1.382-.419.419-.824.679-1.38.896-.42.164-1.065.36-2.235.413-1.274.057-1.649.07-4.859.07-3.211 0-3.586-.015-4.859-.074-1.171-.061-1.816-.256-2.236-.42-2.234.421-4.859.074-4.859-.074-1.171-.061-1.816-.256-2.236-.421-.569-.224-.96-.479-1.379-.899-.421-.419-.69-.824-.9-1.38-.165-.42-.359-1.065-.42-2.235-.045-1.26-.061-1.649-.061-4.844 0-3.196.016-3.586.061-4.861.061-1.17.255-1.814.42-2.234.21-.57.479-.96.9-1.381.419-.419.81-.689 1.379-.898.42-.166 1.051-.361 2.221-.421 1.275-.045 1.65-.06 4.859-.06l.045.03zm0 3.678c-3.405 0-6.162 2.76-6.162 6.162 0 3.405 2.76 6.162 6.162 6.162 3.405 0 6.162-2.76 6.162-6.162 0-3.405-2.76-6.162-6.162-6.162zM12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm7.846-10.405c0-.795-.646-1.44-1.44-1.44-.795 0-1.44.646-1.44 1.44 0 .794.646 1.439 1.44 1.439.793-.001 1.44-.645 1.44-1.439z"/>
                                                    </svg>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Values Section -->
            <section class="py-20 section-gradient">
                <div class="container mx-auto px-6">
                    <div class="text-center mb-16" data-aos="fade-up">
                        <h2 class="text-4xl font-bold mb-4">Our Core Values</h2>
                        <div class="w-24 h-1 accent-gradient mx-auto rounded-full"></div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div class="mission-card p-8 rounded-lg text-center" data-aos="fade-up" data-aos-delay="100">
                            <div class="w-16 h-16 accent-gradient rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                            </div>
                            <h3 class="text-xl font-bold mb-4">Excellence</h3>
                            <p class="text-gray-400">We strive for excellence in every service, ensuring the highest quality service for our customers.</p>
                                </div>
                        <div class="mission-card p-8 rounded-lg text-center" data-aos="fade-up" data-aos-delay="200">
                            <div class="w-16 h-16 accent-gradient rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                            </div>
                            <h3 class="text-xl font-bold mb-4">Innovation</h3>
                            <p class="text-gray-400">We continuously innovate and stay updated with the latest technology trends and repair techniques.</p>
                        </div>
                        <div class="mission-card p-8 rounded-lg text-center" data-aos="fade-up" data-aos-delay="300">
                            <div class="w-16 h-16 accent-gradient rounded-full flex items-center justify-center mx-auto mb-6">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold mb-4">Customer Focus</h3>
                            <p class="text-gray-400">Our customers are at the heart of everything we do, and we're committed to their satisfaction.</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        @include('components.footer')

        <script>
            // Initialize AOS
            AOS.init({
                duration: 800,
                once: false,
                mirror: true,
                easing: 'ease-out-cubic'
            });

            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    document.querySelector(this.getAttribute('href')).scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            });
        </script>
    </body>
</html> 